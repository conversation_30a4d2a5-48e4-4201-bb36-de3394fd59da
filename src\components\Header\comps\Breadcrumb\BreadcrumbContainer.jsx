import React, { useEffect, useState } from "react";
import { AudioLines, Home, Building2, Users, Folder, Shield } from 'lucide-react';
import { useLocation } from "react-router-dom";
import Breadcrumb from "./Breadcrumb";

const BreadcrumbContainer = () => {
  const location = useLocation();
  const [entityName] = useState(null);
 
  const pathIcons = {
    'keystone':       <Home className="svgx" size={14} />,
    'home':           <Home                  size={14} />,
    'organizations':  <Building2             size={14} />,
    'people':         <Users                 size={14} />,
    'cases':          <Folder                size={14} />,
    'agents':         <Shield                size={14} />,
    'informants':     <AudioLines            size={14} />,
  };

  const generatePathSegments = (location) => {
    const paths = location.pathname.split("/").filter(Boolean);
    const baseUrl = "/";
    
    return paths.map((path, index) => {
      // If this is an ID and we have an entity name, use that instead
      if (index === 1 && entityName) {
        return {
          label: entityName,
          link: baseUrl + paths.slice(0, index + 1).join("/"),
        };
      }

      const label = path
        .replace(/-/g, " ")
        .replace(/\b\w/g, (char) => char.toUpperCase());
      
      return {
        label,
        icon: pathIcons[path.toLowerCase()],
        link: baseUrl + paths.slice(0, index + 1).join("/"),
      };
    });
  };

  const pathSegments = generatePathSegments(location);

  return (
    <Breadcrumb 
      pathSegments={[
        { label: "Keystone", icon: pathIcons['keystone'], link: "/" }, 
        ...pathSegments
      ]} 
    />
  );
};

export default BreadcrumbContainer;
