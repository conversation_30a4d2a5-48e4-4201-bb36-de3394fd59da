import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { CREATE_TASK } from '../../../api/mutations';
import { GET_TASKS } from '../../../api/queries';
import styles from '../../../css/modal.module.css';

const CreateTaskModal = ({ isOpen, onClose }) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState('MEDIUM');
  const [level, setLevel] = useState('CL1');
  const [assignedAgentHiveId, setAssignedAgentHiveId] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  const [createTask, { loading }] = useMutation(CREATE_TASK, {
    refetchQueries: [{ query: GET_TASKS }],
    onCompleted: () => {
      onClose();
      setTitle('');
      setDescription('');
      setPriority('MEDIUM');
      setLevel('CL1');
      setAssignedAgentHiveId('');
      setErrorMessage('');
      toast.success('Task created successfully!');
    },
    onError: (error) => {
      setErrorMessage(error.message || 'Could not create task. Please try again.');
      toast.error(error.message || 'Could not create task. Please try again.');
    }
  });

  if (!isOpen) return null;

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrorMessage('');
    if (!title.trim()) {
      setErrorMessage('Title is required.');
      return;
    }
    if (!level) {
      setErrorMessage('Level is required.');
      return;
    }
    createTask({
      variables: {
        title: title.trim(),
        level,
        description: description.trim() || null,
        priority,
        assignedAgentHiveId: assignedAgentHiveId.trim() || null
      }
    });
  };

  const handleClose = () => {
    setTitle('');
    setDescription('');
    setPriority('MEDIUM');
    setLevel('CL1');
    setAssignedAgentHiveId('');
    setErrorMessage('');
    onClose();
  };

  return (
    <div className={styles.modalBackdrop} onClick={handleClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={handleClose}>
          &times;
        </button>
        <div className={styles.modalHeader}>
          <h3>Create New Task</h3>
        </div>
        <form onSubmit={handleSubmit}>
          <div className={styles.modalBody}>
            {errorMessage && <p className={styles.errorMessage}>{errorMessage}</p>}

            <div className={styles.formGroup}>
              <label htmlFor="taskTitle">Title:</label>
              <input
                type="text"
                id="taskTitle"
                className={styles.darkInput}
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="taskDescription">Description:</label>
              <textarea
                id="taskDescription"
                className={styles.darkInput}
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows="3"
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="taskLevel">Level:</label>
              <select
                id="taskLevel"
                className={styles.darkInput}
                value={level}
                onChange={(e) => setLevel(e.target.value)}
                required
              >
                <option value="CL1">CL1</option>
                <option value="CL2">CL2</option>
                <option value="CL3">CL3</option>
                <option value="CL4">CL4</option>
                <option value="CL5">CL5</option>
                <option value="CL6">CL6</option>
                <option value="CL7">CL7</option>
                <option value="CL8">CL8</option>
                <option value="CL9">CL9</option>
                <option value="CLS">CLS</option>
                <option value="CLX">CLX</option>
              </select>
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="taskPriority">Priority:</label>
              <select
                id="taskPriority"
                className={styles.darkInput}
                value={priority}
                onChange={(e) => setPriority(e.target.value)}
              >
                <option value="WISHLIST">Wishlist</option>
                <option value="LOW">Low</option>
                <option value="MEDIUM">Medium</option>
                <option value="HIGH">High</option>
                <option value="UFN">UFN</option>
              </select>
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="assignedAgent">Assigned Agent Hive ID:</label>
              <input
                type="text"
                id="assignedAgent"
                className={styles.darkInput}
                value={assignedAgentHiveId}
                onChange={(e) => setAssignedAgentHiveId(e.target.value)}
                placeholder="Optional Agent Hive ID"
              />
            </div>
          </div>
          <div className={styles.modalFooter}>
            <button type="button" className={styles.secondary} onClick={handleClose}>
              Cancel
            </button>
            <button type="submit" className={styles.primary} disabled={loading}>
              {loading ? 'Creating...' : 'Create Task'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateTaskModal;
