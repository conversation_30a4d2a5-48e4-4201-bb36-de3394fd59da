.ckpnt-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #1a1a1a;
}

.ckpnt-box {
  background: #222;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.ckpnt-box h2 {
  text-align: center;
  margin-bottom: 1.5rem;
  color: #ccc;
}

.ckpnt-form-group {
  margin-bottom: 1rem;
}

.ckpnt-form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #ccc;
}

.ckpnt-form-group input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #444;
  border-radius: 4px;
  font-size: 1rem;
  background-color: #333 !important; 
  color: #fff;
}

.ckpnt-form-group input:focus {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #444;
    border-radius: 4px;
    font-size: 1rem;
    background-color: #333 !important; 
    color: #fff;
  }

.ckpnt-form-group input:focus {
  outline: none;
  border-color: #EAA216;
}

.ckpnt-button {
  width: 100%;
  padding: 0.75rem;
  background-color: #313131;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  margin-top: 1rem;
  transition: all 0.3s ease;
}

.ckpnt-button:hover {
  background-color: #564920;
}

.ckpnt-error-message {
  color: #dc3545;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: #111;
  border-radius: 4px;
}

.ckpnt-link {
  text-align: center;
  margin-top: 1rem;
  color: #ccc;
}

.ckpnt-link a {
  color: #EAA216;
  text-decoration: none;
}

.ckpnt-link a:hover {
  text-decoration: underline;
} 