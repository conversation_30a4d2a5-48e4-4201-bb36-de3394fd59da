import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { ADD_SUSPECT_TO_CASE, REMOVE_SUSPECT_FROM_CASE } from '../../../api/mutations';
import styles from '../../../css/modal.module.css';
import '../../../css/shared.css';

const ManageSuspectsModal = ({ isOpen, onClose, caseData, onRefetch }) => {
  const [personHiveId, setPersonHiveId] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [confirmRemove, setConfirmRemove] = useState(null);

  const [addSuspectMutation, { loading: addLoading }] = useMutation(ADD_SUSPECT_TO_CASE, {
    onCompleted: () => {
      onRefetch(); // This will refresh the case data and update the modal
      setPersonHiveId(''); // Clear the input field
      setErrorMessage(''); // Clear any error messages
      toast.success('Suspect added to case successfully!');
    },
    onError: (error) => {
      setErrorMessage(error.message || 'Could not add suspect to case. Please try again.');
      toast.error(error.message || 'Could not add suspect to case. Please try again.');
    }
  });

  const [removeSuspectMutation, { loading: removeLoading }] = useMutation(REMOVE_SUSPECT_FROM_CASE, {
    onCompleted: () => {
      onRefetch();
      setConfirmRemove(null);
      toast.success('Suspect removed from case successfully!');
    },
    onError: (error) => {
      setConfirmRemove(null);
      toast.error(error.message || 'Could not remove suspect from case. Please try again.');
    }
  });

  if (!isOpen || !caseData) return null;

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrorMessage('');

    if (!personHiveId.trim()) {
      setErrorMessage('Person Hive ID is required.');
      return;
    }

    const alreadySuspect = caseData.suspects?.some(s => s.hiveId === personHiveId.trim());
    if (alreadySuspect) {
      setErrorMessage('This person is already a suspect in this case.');
      return;
    }

    addSuspectMutation({
      variables: {
        personHiveId: personHiveId.trim(),
        caseHiveId: caseData.hiveId
      }
    });
  };

  const handleRemove = (suspect) => {
    setConfirmRemove(suspect);
  };

  const confirmRemoveAction = () => {
    if (confirmRemove) {
      removeSuspectMutation({
        variables: {
          personHiveId: confirmRemove.hiveId,
          caseHiveId: caseData.hiveId
        }
      });
    }
  };

  const cancelRemove = () => {
    setConfirmRemove(null);
  };

  const handleClose = () => {
    setPersonHiveId('');
    setErrorMessage('');
    setConfirmRemove(null);
    onClose();
  };

  return (
    <div className={styles.modalBackdrop} onClick={handleClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={handleClose}>
          &times;
        </button>
        <div className={styles.modalHeader}>
          <h3>Manage Suspects for Case ID {caseData.hiveId}</h3>
          <p style={{ fontSize: '0.9rem', color: '#ccc', margin: '0.5rem 0 0 0' }}>
            Case: {caseData.title}
          </p>
        </div>
        <div className={styles.modalBody}>
          {errorMessage && <p className={styles.errorMessage}>{errorMessage}</p>}

          {/* Current Suspects Section */}
          <div style={{ marginBottom: '1.5rem' }}>
            <label style={{ color: '#ccc', fontSize: '0.9rem', fontWeight: 'bold' }}>Current Suspects:</label>
            {caseData.suspects && caseData.suspects.length > 0 ? (
              <div style={{
                margin: '0.5rem 0',
                padding: '0.5rem',
                backgroundColor: '#2a2a2a',
                borderRadius: '4px',
                border: '1px solid #444'
              }}>
                {caseData.suspects.map((suspect) => (
                  <div key={suspect.hiveId} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '0.5rem 0',
                    borderBottom: '1px solid #333'
                  }}>
                    <span style={{ color: '#e0e0e0', fontSize: '0.9rem' }}>
                      {suspect.firstName} {suspect.lastName} (ID: {suspect.hiveId})
                    </span>
                    <button
                      type="button"
                      onClick={() => handleRemove(suspect)}
                      className="button-remove-small"
                      title={`Remove ${suspect.firstName} ${suspect.lastName} from case`}
                      disabled={removeLoading}
                    >
                      -
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <p style={{ color: '#888', fontSize: '0.8rem', margin: '0.5rem 0' }}>
                No suspects assigned to this case.
              </p>
            )}
          </div>

          {/* Add New Suspect Section */}
          <form onSubmit={handleSubmit}>
            <div style={{ borderTop: '1px solid #444', paddingTop: '1rem' }}>
              <label style={{ color: '#ccc', fontSize: '0.9rem', fontWeight: 'bold' }}>Add New Suspect:</label>
              <div className={styles.formGroup} style={{ marginTop: '0.5rem' }}>
                <label htmlFor="suspectHiveId">Person Hive ID:</label>
                <input
                  type="text"
                  id="suspectHiveId"
                  className={styles.darkInput}
                  value={personHiveId}
                  onChange={(e) => setPersonHiveId(e.target.value)}
                  placeholder="Enter Person Hive ID"
                  required
                />
              </div>
            </div>
            <div className={styles.modalFooter}>
              <button type="button" className={styles.secondary} onClick={handleClose}>
                Close
              </button>
              <button type="submit" className={styles.primary} disabled={addLoading}>
                {addLoading ? 'Adding...' : 'Add Suspect'}
              </button>
            </div>
          </form>
        </div>

        {/* Confirmation Dialog */}
        {confirmRemove && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1001
          }}>
            <div style={{
              backgroundColor: '#222',
              padding: '1.5rem',
              borderRadius: '8px',
              maxWidth: '400px',
              width: '90%'
            }}>
              <h4 style={{ color: '#fff', marginBottom: '1rem' }}>Confirm Removal</h4>
              <p style={{ color: '#ccc', marginBottom: '1.5rem' }}>
                Are you sure you want to remove <strong>{confirmRemove.firstName} {confirmRemove.lastName}</strong> as a suspect from this case?
              </p>
              <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
                <button
                  onClick={cancelRemove}
                  className={styles.primary}
                  disabled={removeLoading}
                >
                  Cancel
                </button>
                <button
                  onClick={confirmRemoveAction}
                  className={styles.secondary}
                  disabled={removeLoading}
                >
                  {removeLoading ? 'Removing...' : 'Remove'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ManageSuspectsModal;
