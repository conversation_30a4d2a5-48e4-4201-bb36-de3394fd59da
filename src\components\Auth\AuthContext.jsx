import React, { createContext, useState, useContext } from 'react';
import client from '../../../api/apolloClient';
import { getUserDataFromCookie, isAuthenticatedViaCookie, clearAuthCookies } from '../../utils/cookieUtils';
import { LOGOUT_MUTATION } from '../../../api/mutations';

const AuthContext = createContext();

export function AuthProvider({ children }) {
  const [user, setUser] = useState(() => {
    // Initialize user from cookie
    return getUserDataFromCookie();
  });
  const [isAuthenticated, setIsAuthenticated] = useState(() => {
    // Check if user is authenticated via cookies
    return isAuthenticatedViaCookie();
  });

  const login = (userData) => {
    setUser(userData);
    setIsAuthenticated(true);
  };

  const logout = async () => {
    try {
      // Call server-side logout to clear cookies
      await client.mutate({
        mutation: LOGOUT_MUTATION
      });
    } catch (error) {
      console.error('Logout error:', error);
      // Continue with client-side logout even if server call fails
    }

    // Clear client-side state
    setUser(null);
    setIsAuthenticated(false);
    clearAuthCookies(); // Clear authentication cookies client-side as backup
    if (client) {
      client.resetStore();  // Clear client cache
    }
  };

  const checkAuthentication = () => {
    return isAuthenticated;
  };

  const isAdmin = () => {
    return user?.role === 'ADMIN';
  };

  const isDeveloper = () => {
    return user?.role === 'DEVELOPER';
  };

  const hasClearance = (level) => {
    if (!user?.clearance) return false;
    return user.clearance >= level;
  };

  return (
    <AuthContext.Provider value={{ user, login, logout, isAuthenticated: checkAuthentication, isAdmin, isDeveloper, hasClearance }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  return useContext(AuthContext);
} 