import React, { useState, useEffect } from 'react';
import styles from '../../../css/modal.module.css';

const SetVehicleOwnerForm = ({ onSubmit, onCancel, currentOwnerHiveId, vehicleName }) => {
  const [ownerHiveIdInput, setOwnerHiveIdInput] = useState(currentOwnerHiveId || '');
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    setOwnerHiveIdInput(currentOwnerHiveId || '');
  }, [currentOwnerHiveId]);

  const handleInternalSubmit = (e) => {
    e.preventDefault();
    setErrorMessage('');
    if (!ownerHiveIdInput.trim()) {
      setErrorMessage('Owner Hive ID is required.');
      return;
    }
    onSubmit({ ownerHiveId: ownerHiveIdInput.trim() });
  };

  return (
    <form onSubmit={handleInternalSubmit}>
        <div className={styles.modalHeader}>
            <h4>Set Owner for {vehicleName}</h4>
        </div>
        <div className={styles.modalBody}>
            {errorMessage && <p className={styles.errorMessage}>{errorMessage}</p>}
            <div className={styles.formGroup}>
                <label htmlFor="ownerHiveIdInput">Owner Hive ID:</label>
                <input 
                    type="text"
                    id="ownerHiveIdInput"
                    value={ownerHiveIdInput}
                    onChange={(e) => setOwnerHiveIdInput(e.target.value)}
                    className={styles.darkInput}
                    placeholder="Enter Person or Organization Hive ID"
                    required 
                />
            </div>
        </div>
        <div className={styles.modalFooter}>
            <button type="button" className={styles.secondary} onClick={onCancel}>Cancel</button>
            <button type="submit" className={styles.primary}>Set Owner</button>
        </div>
    </form>
  );
};

const SetVehicleOwnerModal = ({ isOpen, onClose, vehicle, onSubmitSetOwner }) => {
  if (!isOpen || !vehicle) return null;

  const currentOwnerHiveId = vehicle.owner && vehicle.owner.length > 0 ? vehicle.owner[0]?.hiveId : null;
  const vehicleName = `${vehicle.make} ${vehicle.model}`;

  const handleFormSubmitRelay = (formData) => {
    onSubmitSetOwner(formData);
  };

  return (
    <div className={styles.modalBackdrop} onClick={onClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={onClose}>&times;</button>
        
        <SetVehicleOwnerForm
            onSubmit={handleFormSubmitRelay}
            onCancel={onClose}
            currentOwnerHiveId={currentOwnerHiveId}
            vehicleName={vehicleName}
        />
      </div>
    </div>
  );
};

export default SetVehicleOwnerModal; 