import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { SET_TASK_SCOPE } from '../../../api/mutations';
import styles from '../../../css/modal.module.css';

const SetTaskScopeModal = ({ isOpen, onClose, task, onRefetch }) => {
  const [scopeHiveId, setScopeHiveId] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  const [setScopeMutation, { loading }] = useMutation(SET_TASK_SCOPE, {
    onCompleted: () => {
      onRefetch();
      onClose();
      setScopeHiveId('');
      setErrorMessage('');
      toast.success('Scope set successfully!');
    },
    onError: (error) => {
      setErrorMessage(error.message || 'Could not set scope. Please try again.');
      toast.error(error.message || 'Could not set scope. Please try again.');
    }
  });

  if (!isOpen || !task) return null;

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrorMessage('');
    if (!scopeHiveId.trim()) {
      setErrorMessage('Hive ID is required.');
      return;
    }
    setScopeMutation({ variables: { taskHiveId: task.hiveId, scopeHiveId: scopeHiveId.trim() } });
  };

  const handleClose = () => {
    setScopeHiveId('');
    setErrorMessage('');
    onClose();
  };

  return (
    <div className={styles.modalBackdrop} onClick={handleClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={handleClose}>&times;</button>
        <div className={styles.modalHeader}>
          <h3>Set Task Scope</h3>
          <p style={{ fontSize: '0.9rem', color: '#ccc', margin: '0.5rem 0 0 0' }}>
            Task: {task.title}
          </p>
        </div>
        <form onSubmit={handleSubmit}>
          <div className={styles.modalBody}>
            {errorMessage && <p className={styles.errorMessage}>{errorMessage}</p>}
            <div className={styles.formGroup}>
              <label htmlFor="scopeHiveId">Case/Operation Hive ID:</label>
              <input
                type="text"
                id="scopeHiveId"
                className={styles.darkInput}
                value={scopeHiveId}
                onChange={(e) => setScopeHiveId(e.target.value)}
                placeholder="Enter Case or Operation Hive ID"
                required
              />
            </div>
          </div>
          <div className={styles.modalFooter}>
            <button type="button" className={styles.secondary} onClick={handleClose}>
              Cancel
            </button>
            <button type="submit" className={styles.primary} disabled={loading}>
              {loading ? 'Saving...' : 'Set Scope'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SetTaskScopeModal;
