import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { ADD_TARGET_TO_OPERATION, REMOVE_TARGET_FROM_OPERATION } from '../../../api/mutations';
import styles from '../../../css/modal.module.css';
import '../../../css/shared.css';

const ManageTargetsModal = ({ isOpen, onClose, operation, onRefetch }) => {
  const [targetHiveId, setTargetHiveId] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [confirmRemove, setConfirmRemove] = useState(null);

  const [addTargetMutation, { loading: addLoading }] = useMutation(ADD_TARGET_TO_OPERATION, {
    onCompleted: () => {
      onRefetch();
      setTargetHiveId('');
      setErrorMessage('');
      toast.success('Target added to operation successfully!');
    },
    onError: (error) => {
      setErrorMessage(error.message || 'Could not add target to operation. Please try again.');
      toast.error(error.message || 'Could not add target to operation. Please try again.');
    }
  });

  const [removeTargetMutation, { loading: removeLoading }] = useMutation(REMOVE_TARGET_FROM_OPERATION, {
    onCompleted: () => {
      onRefetch();
      setConfirmRemove(null);
      toast.success('Target removed from operation successfully!');
    },
    onError: (error) => {
      console.error('Error removing target:', error);
      toast.error(`Error removing target: ${error.message}`);
      setConfirmRemove(null);
    }
  });

  const formatTargetName = (target) => {
    switch (target.__typename) {
      case 'Person':
        return `${target.firstName} ${target.lastName}`;
      case 'Organization':
        return target.name;
      case 'Vehicle':
        return `${target.make} ${target.model} (${target.color})`;
      default:
        return 'Unknown Target';
    }
  };

  if (!isOpen || !operation) return null;

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrorMessage('');

    if (!targetHiveId.trim()) {
      setErrorMessage('Target Hive ID is required.');
      return;
    }

    // Check if target is already in the operation
    const isAlreadyTarget = operation.targets?.some(target => target.hiveId === targetHiveId.trim());
    if (isAlreadyTarget) {
      setErrorMessage('This target is already assigned to this operation.');
      return;
    }

    addTargetMutation({
      variables: {
        targetHiveId: targetHiveId.trim(),
        operationHiveId: operation.hiveId
      }
    });
  };

  const handleRemove = (target) => {
    setConfirmRemove(target);
  };

  const confirmRemoveAction = () => {
    if (confirmRemove) {
      removeTargetMutation({
        variables: {
          targetHiveId: confirmRemove.hiveId,
          operationHiveId: operation.hiveId
        }
      });
    }
  };

  const cancelRemove = () => {
    setConfirmRemove(null);
  };

  const handleClose = () => {
    setTargetHiveId('');
    setErrorMessage('');
    setConfirmRemove(null);
    onClose();
  };

  return (
    <div className={styles.modalBackdrop} onClick={handleClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={handleClose}>
          &times;
        </button>
        <div className={styles.modalHeader}>
          <h3>Manage Targets for Operation</h3>
          <p style={{ fontSize: '0.9rem', color: '#ccc', margin: '0.5rem 0 0 0' }}>
            Operation: {operation.title}
          </p>
        </div>
        <div className={styles.modalBody}>
          {errorMessage && <p className={styles.errorMessage}>{errorMessage}</p>}

          {/* Current Targets Section */}
          <div style={{ marginBottom: '1.5rem' }}>
            <label style={{ color: '#ccc', fontSize: '0.9rem', fontWeight: 'bold' }}>Current Targets:</label>
            {operation.targets && operation.targets.length > 0 ? (
              <div style={{
                margin: '0.5rem 0',
                padding: '0.5rem',
                backgroundColor: '#2a2a2a',
                borderRadius: '4px',
                border: '1px solid #444'
              }}>
                {operation.targets.map((target) => (
                  <div key={target.hiveId} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '0.5rem 0',
                    borderBottom: '1px solid #333'
                  }}>
                    <span style={{ color: '#e0e0e0', fontSize: '0.9rem' }}>
                      {formatTargetName(target)} (ID: {target.hiveId})
                    </span>
                    <button
                      type="button"
                      onClick={() => handleRemove(target)}
                      className="button-remove-small"
                      title="Remove target"
                    >
                      -
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <p style={{ color: '#888', fontSize: '0.9rem', margin: '0.5rem 0', fontStyle: 'italic' }}>
                No targets assigned to this operation.
              </p>
            )}
          </div>

          {/* Add New Target Section */}
          <form onSubmit={handleSubmit}>
            <div style={{ borderTop: '1px solid #444', paddingTop: '1rem' }}>
              <label style={{ color: '#ccc', fontSize: '0.9rem', fontWeight: 'bold' }}>Add New Target:</label>
              <div className={styles.formGroup} style={{ marginTop: '0.5rem' }}>
                <label htmlFor="targetHiveId">Target Hive ID:</label>
                <input
                  type="text"
                  id="targetHiveId"
                  className={styles.darkInput}
                  value={targetHiveId}
                  onChange={(e) => setTargetHiveId(e.target.value)}
                  placeholder="Enter Person, Organization, or Vehicle Hive ID"
                  required
                />
              </div>
            </div>
            <div className={styles.modalFooter}>
              <button type="button" className={styles.secondary} onClick={handleClose}>
                Close
              </button>
              <button type="submit" className={styles.primary} disabled={addLoading}>
                {addLoading ? 'Adding...' : 'Add Target'}
              </button>
            </div>
          </form>
        </div>

        {/* Confirmation Dialog */}
        {confirmRemove && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1001
          }}>
            <div style={{
              backgroundColor: '#222',
              padding: '1.5rem',
              borderRadius: '8px',
              maxWidth: '400px',
              width: '90%'
            }}>
              <h4 style={{ color: '#fff', marginBottom: '1rem' }}>Confirm Removal</h4>
              <p style={{ color: '#ccc', marginBottom: '1.5rem' }}>
                Are you sure you want to remove <strong>{formatTargetName(confirmRemove)}</strong> from this operation?
              </p>
              <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
                <button
                  onClick={cancelRemove}
                  className={styles.primary}
                  style={{ backgroundColor: '#3a3a3a' }}
                >
                  Cancel
                </button>
                <button
                  onClick={confirmRemoveAction}
                  className={styles.secondary}
                  disabled={removeLoading}
                >
                  {removeLoading ? 'Removing...' : 'Remove'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ManageTargetsModal;
