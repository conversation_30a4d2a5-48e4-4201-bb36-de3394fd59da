import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { getCookie } from '../src/utils/cookieUtils';

const httpLink = createHttpLink({
  uri: 'http://localhost:4000/graphql',
  credentials: 'include' // This ensures cookies are sent with requests
});

// Add CSRF token to headers for all requests
const authLink = setContext((_, { headers }) => {
  // Get CSRF token from cookie
  const csrfToken = getCookie('csrfToken');

  return {
    headers: {
      ...headers,
      // Add CSRF token header if it exists
      ...(csrfToken && { 'x-csrf-token': csrfToken }),
    }
  };
});

const client = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache(),
});

export default client;