import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { SET_LEAD_AGENT_FOR_OPERATION } from '../../../api/mutations';
import styles from '../../../css/modal.module.css';
import '../../../css/shared.css';

const AssignLeadAgentModal = ({ isOpen, onClose, operation, onRefetch }) => {
  const [agentHiveId, setAgentHiveId] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  const [setLeadAgentMutation, { loading }] = useMutation(SET_LEAD_AGENT_FOR_OPERATION, {
    onCompleted: () => {
      onRefetch();
      onClose();
      setAgentHiveId('');
      setErrorMessage('');
      toast.success('Lead agent assigned successfully!');
    },
    onError: (error) => {
      setErrorMessage(error.message || 'Could not assign lead agent. Please try again.');
      toast.error(error.message || 'Could not assign lead agent. Please try again.');
    }
  });

  if (!isOpen || !operation) return null;

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrorMessage('');
    
    if (!agentHiveId.trim()) {
      setErrorMessage('Agent Hive ID is required.');
      return;
    }

    // Check if this agent is already the lead agent
    const currentLeadAgent = operation.leadAgent && operation.leadAgent.length > 0 ? operation.leadAgent[0] : null;
    if (currentLeadAgent && currentLeadAgent.hiveId === agentHiveId.trim()) {
      setErrorMessage('This agent is already the lead agent for this operation.');
      return;
    }

    setLeadAgentMutation({
      variables: {
        agentHiveId: agentHiveId.trim(),
        operationHiveId: operation.hiveId
      }
    });
  };

  const handleClose = () => {
    setAgentHiveId('');
    setErrorMessage('');
    onClose();
  };

  const currentLeadAgent = operation.leadAgent && operation.leadAgent.length > 0 ? operation.leadAgent[0] : null;

  return (
    <div className={styles.modalBackdrop} onClick={handleClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={handleClose}>
          &times;
        </button>
        <div className={styles.modalHeader}>
          <h3>Assign Lead Agent</h3>
          <p style={{ fontSize: '0.9rem', color: '#ccc', margin: '0.5rem 0 0 0' }}>
            Operation: {operation.title}
          </p>
        </div>
        <form onSubmit={handleSubmit}>
          <div className={styles.modalBody}>
            {errorMessage && <p className={styles.errorMessage}>{errorMessage}</p>}
            
            <div className={styles.formGroup}>
              <label htmlFor="agentHiveId">Agent Hive ID:</label>
              <input
                type="text"
                id="agentHiveId"
                className={styles.darkInput}
                value={agentHiveId}
                onChange={(e) => setAgentHiveId(e.target.value)}
                placeholder="Enter Agent Hive ID"
                required
              />
              <small style={{ color: '#888', fontSize: '0.8rem' }}>
                Enter the Hive ID of an Agent to assign as the lead for this operation.
              </small>
            </div>

            {/* Show current lead agent for reference */}
            {currentLeadAgent && (
              <div style={{ marginTop: '1rem' }}>
                <label style={{ color: '#ccc', fontSize: '0.9rem' }}>Current Lead Agent:</label>
                <div style={{ margin: '0.5rem 0', padding: '0.5rem', backgroundColor: '#333', borderRadius: '4px' }}>
                  <p style={{ margin: 0, fontSize: '0.8rem', color: '#ccc' }}>
                    <strong>{currentLeadAgent.username}</strong>
                    {currentLeadAgent.backingPerson && currentLeadAgent.backingPerson.length > 0 && (
                      <span> ({currentLeadAgent.backingPerson[0].firstName} {currentLeadAgent.backingPerson[0].lastName})</span>
                    )}
                    <br />
                    <span style={{ color: '#888' }}>ID: {currentLeadAgent.hiveId}</span>
                  </p>
                </div>
                <small style={{ color: '#888', fontSize: '0.8rem' }}>
                  Assigning a new lead agent will replace the current one.
                </small>
              </div>
            )}
          </div>
          <div className={styles.modalFooter}>
            <button type="button" className={styles.secondary} onClick={handleClose}>
              Cancel
            </button>
            <button type="submit" className={styles.primary} disabled={loading}>
              {loading ? 'Assigning...' : 'Assign Lead Agent'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AssignLeadAgentModal;
