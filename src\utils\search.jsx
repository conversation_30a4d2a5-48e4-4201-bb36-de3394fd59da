export const api = {
  async fetchApi(url, options = {}) {
    const response = await fetch(url, options);
    if (!response.ok) {
      throw new Error(`Request failed with status ${response.status}`);
    }
    return response.json();
  },
  getUrl(key) {
    const routes = {
      SEARCH: '/api/entities'
    };
    return routes[key] || '';
  }
};

export async function lookupEntity(id) {
  if (!id) return null;
  try {
    return await api.fetchApi(`${api.getUrl('SEARCH')}/${id}`);
  } catch (err) {
    console.error('Lookup failed:', err);
    return null;
  }
}

export function renderPreviewCard(entity) {
  if (!entity) return null;
  return (
    <div style={{ border: '1px solid #444', padding: '8px', borderRadius: '6px' }}>
      <div style={{ fontWeight: 'bold' }}>{entity.name || entity.hiveId}</div>
      {entity.role && (
        <div style={{ fontSize: '0.85rem', color: '#ccc' }}>{entity.role}</div>
      )}
    </div>
  );
}

export function InvalidEntity({ id }) {
  return (
    <div style={{ padding: '8px', border: '1px solid #444', borderRadius: '6px' }}>
      {`Entity with ID "${id}" not found.`}
    </div>
  );
}
