import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { SET_TASK_PRIORITY } from '../../../api/mutations';
import styles from '../../../css/modal.module.css';

const SetTaskPriorityModal = ({ isOpen, onClose, task, onRefetch }) => {
  const [priority, setPriority] = useState('MEDIUM');
  const [errorMessage, setErrorMessage] = useState('');

  const [setPriorityMutation, { loading }] = useMutation(SET_TASK_PRIORITY, {
    onCompleted: () => {
      onRefetch();
      onClose();
      setErrorMessage('');
      toast.success('Priority updated successfully!');
    },
    onError: (error) => {
      setErrorMessage(error.message || 'Could not set priority. Please try again.');
      toast.error(error.message || 'Could not set priority. Please try again.');
    }
  });

  if (!isOpen || !task) return null;

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrorMessage('');
    setPriorityMutation({ variables: { taskHiveId: task.hiveId, priority } });
  };

  const handleClose = () => {
    setErrorMessage('');
    onClose();
  };

  return (
    <div className={styles.modalBackdrop} onClick={handleClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={handleClose}>&times;</button>
        <div className={styles.modalHeader}>
          <h3>Set Task Priority</h3>
          <p style={{ fontSize: '0.9rem', color: '#ccc', margin: '0.5rem 0 0 0' }}>
            Task: {task.title}
          </p>
        </div>
        <form onSubmit={handleSubmit}>
          <div className={styles.modalBody}>
            {errorMessage && <p className={styles.errorMessage}>{errorMessage}</p>}
            <div className={styles.formGroup}>
              <label htmlFor="taskPriority">Priority:</label>
              <select
                id="taskPriority"
                className={styles.darkInput}
                value={priority}
                onChange={(e) => setPriority(e.target.value)}
                required
              >
                <option value="WISHLIST">Wishlist</option>
                <option value="LOW">Low</option>
                <option value="MEDIUM">Medium</option>
                <option value="HIGH">High</option>
                <option value="UFN">UFN</option>
              </select>
            </div>
          </div>
          <div className={styles.modalFooter}>
            <button type="button" className={styles.secondary} onClick={handleClose}>
              Cancel
            </button>
            <button type="submit" className={styles.primary} disabled={loading}>
              {loading ? 'Saving...' : 'Set Priority'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SetTaskPriorityModal;
