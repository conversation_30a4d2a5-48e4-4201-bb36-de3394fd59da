import React, { useState } from 'react';
import { FiChevronsLeft, FiChevronsRight, } from 'react-icons/fi';
import { Settings } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import styles from '../../../../css/sidebar.module.css';


export default function BottomPanel({ isCollapsed, handleCollapse }) {
  const [isDarkMode, setIsDarkMode] = useState(true); // Track theme state
  const navigate = useNavigate(); // Enable navigation for Settings button

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
    document.body.classList.toggle('light-mode', !isDarkMode);
  };

  return (
    <div className={`${styles['bottom-panel']} ${isCollapsed ? styles.collapsed : ''}`}>



      {/* Collapse */}
      <button className={styles['collapse-button']} onClick={handleCollapse}>
        {isCollapsed ? <FiChevronsRight size={22} /> : <FiChevronsLeft size={22} />}
      </button>

      {/* Settings */}
      <button className={styles['bottom-panel-button']} onClick={() => navigate('/settings')} >
        <Settings size={22} />
      </button>
      
    </div>
  );
}