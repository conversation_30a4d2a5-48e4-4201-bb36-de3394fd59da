// Cookie utility functions for authentication

/**
 * Get a cookie value by name
 * @param {string} name - Cookie name
 * @returns {string|null} Cookie value or null if not found
 */
export function getCookie(name) {
  if (typeof document === 'undefined') {
    return null; // Server-side rendering protection
  }
  
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop().split(';').shift();
  }
  return null;
}

/**
 * Set a cookie (for non-httpOnly cookies only)
 * @param {string} name - Cookie name
 * @param {string} value - Cookie value
 * @param {Object} options - Cookie options
 */
export function setCookie(name, value, options = {}) {
  if (typeof document === 'undefined') {
    return; // Server-side rendering protection
  }
  
  const {
    maxAge = 12 * 60 * 60 * 1000, // 12 hours default
    path = '/',
    secure = window.location.protocol === 'https:',
    sameSite = 'strict'
  } = options;
  
  let cookieString = `${name}=${value}; path=${path}; max-age=${Math.floor(maxAge / 1000)}; samesite=${sameSite}`;
  
  if (secure) {
    cookieString += '; secure';
  }
  
  document.cookie = cookieString;
}

/**
 * Delete a cookie by setting it to expire
 * @param {string} name - Cookie name
 * @param {string} path - Cookie path (default: '/')
 */
export function deleteCookie(name, path = '/') {
  if (typeof document === 'undefined') {
    return; // Server-side rendering protection
  }
  
  document.cookie = `${name}=; path=${path}; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
}

/**
 * Get user data from agentData cookie
 * @returns {Object|null} Parsed user data or null if not found
 */
export function getUserDataFromCookie() {
  const agentData = getCookie('agentData');
  if (!agentData) {
    return null;
  }
  
  try {
    return JSON.parse(decodeURIComponent(agentData));
  } catch (error) {
    console.error('Failed to parse agent data from cookie:', error);
    return null;
  }
}

// Check if cookies are enabled in the browser
// @returns {boolean} True if cookies are enabled

export function areCookiesEnabled() {
  if (typeof document === 'undefined') {
    return false; // Server-side rendering protection
  }
  
  try {
    // Try to set a test cookie
    const testCookie = 'cookieTest=1';
    document.cookie = testCookie;
    const cookiesEnabled = document.cookie.indexOf(testCookie) !== -1;
    
    // Clean up test cookie
    if (cookiesEnabled) {
      document.cookie = 'cookieTest=; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    }
    
    return cookiesEnabled;
  } catch (error) {
    return false;
  }
}

/**
 * Check if the user is authenticated by checking for agentAuth cookie
 * Note: We can't read the httpOnly agentAuth cookie directly, but we can check
 * if the agentData cookie exists as an indicator
 * @returns {boolean} True if likely authenticated
 */
export function isAuthenticatedViaCookie() {
  return !!getUserDataFromCookie();
}

// Clear all authentication-related cookies
export function clearAuthCookies() {
  deleteCookie('agentAuth');
  deleteCookie('agentData');
  deleteCookie('csrfToken');
}
