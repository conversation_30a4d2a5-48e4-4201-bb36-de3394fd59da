import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { CREATE_INFORMANT } from '../../../api/mutations';
import { GET_INFORMANTS } from '../../../api/queries';
import styles from '../../../css/modal.module.css';

const CreateInformantModal = ({ isOpen, onClose }) => {
  const [codeName, setCodeName] = useState('');
  const [backingPersonHiveId, setBackingPersonHiveId] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  const [createInformant, { loading }] = useMutation(CREATE_INFORMANT, {
    refetchQueries: [{ query: GET_INFORMANTS }],
    onError: (error) => {
      setErrorMessage(error.graphQLErrors && error.graphQLErrors.length > 0 ? error.graphQLErrors[0].message : error.message || 'Could not create informant. Please try again.');
      toast.error(error.graphQLErrors && error.graphQLErrors.length > 0 ? error.graphQLErrors[0].message : error.message || 'Could not create informant. Please try again.');
    },
    onCompleted: () => {
      onClose(); 
      setCodeName('');
      setBackingPersonHiveId('');
      setErrorMessage('');
      toast.success('Informant created successfully!');
    },
  });

  if (!isOpen) return null;

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrorMessage('');
    if (!codeName.trim() || !backingPersonHiveId.trim()) {
      setErrorMessage('Code Name and Backing Person Hive ID are required.');
      return;
    }
    try {
      await createInformant({ variables: { codeName, backingPersonHiveId } });
    } catch (err) {
      console.error('Submission error:', err);
      if (!errorMessage) {
        setErrorMessage('An unexpected error occurred during submission.');
      }
    }
  };

  return (
    <div className={styles.modalBackdrop} onClick={onClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={onClose}>
          &times;
        </button>
        <div className={styles.modalHeader}>
          <h3>Create New Informant</h3>
        </div>
        <form onSubmit={handleSubmit}>
          <div className={styles.modalBody}>
            {errorMessage && <p style={{ color: 'red' }}>{errorMessage}</p>}
            <div>
              <label htmlFor="informantCodeName">Code Name:</label>
              <input
                type="text"
                id="informantCodeName"
                className={styles.darkInput}
                value={codeName}
                onChange={(e) => setCodeName(e.target.value)}
                required
                disabled={loading}
              />
            </div>
            <div>
              <label htmlFor="informantBackingPersonHiveId">Backing Person Hive ID:</label>
              <input
                type="text"
                id="informantBackingPersonHiveId"
                className={styles.darkInput}
                value={backingPersonHiveId}
                onChange={(e) => setBackingPersonHiveId(e.target.value)}
                required
                disabled={loading}
              />
            </div>
          </div>
          <div className={styles.modalFooter}>
            <button type="button" className={styles.secondary} onClick={onClose} disabled={loading}>
              Cancel
            </button>
            <button type="submit" className={styles.primary} disabled={loading}>
              {loading ? 'Creating...' : 'Create Informant'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateInformantModal; 