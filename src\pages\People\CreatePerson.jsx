import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { CREATE_PERSON } from '../../../api/mutations';
import { GET_PEOPLE } from '../../../api/queries';
import styles from '../../../css/modal.module.css';

const CreatePersonModal = ({ isOpen, onClose }) => {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [dateOfBirth, setDateOfBirth] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  const [createPerson, { loading }] = useMutation(CREATE_PERSON, {
    refetchQueries: [{ query: GET_PEOPLE }],
    onError: (error) => {
      setErrorMessage(error.message || 'Could not create person. Please try again.');
      toast.error(error.message || 'Could not create person. Please try again.');
    },
    onCompleted: () => {
      onClose(); // Close modal on successful creation
      setFirstName(''); // Reset form
      setLastName('');
      setDateOfBirth('');
      setErrorMessage('');
      toast.success('Person created successfully!');
    },
  });

  if (!isOpen) return null;

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrorMessage('');
    if (!firstName.trim() || !lastName.trim() || !dateOfBirth) {
      setErrorMessage('First Name, Last Name, and Date of Birth are required.');
      return;
    }
    try {
      await createPerson({ variables: { firstName, lastName, dateOfBirth } });
    } catch (err) {
      // Error is handled by onError in useMutation
      console.error('Submission error:', err);
    }
  };

  return (
    <div className={styles.modalBackdrop} onClick={onClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={onClose}>
          &times;
        </button>
        <div className={styles.modalHeader}>
          <h3>Create New Person</h3>
        </div>
        <form onSubmit={handleSubmit}>
          <div className={styles.modalBody}>
            {errorMessage && <p style={{ color: 'red' }}>{errorMessage}</p>}
            <div>
              <label htmlFor="personFirstName">First Name:</label>
              <input
                type="text"
                id="personFirstName"
                className={styles.darkInput}
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                required
                disabled={loading}
              />
            </div>
            <div>
              <label htmlFor="personLastName">Last Name:</label>
              <input
                type="text"
                id="personLastName"
                className={styles.darkInput}
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                required
                disabled={loading}
              />
            </div>
            <div>
              <label htmlFor="personDateOfBirth">Date of Birth:</label>
              <input
                type="date"
                id="personDateOfBirth"
                className={styles.darkInput}
                value={dateOfBirth}
                onChange={(e) => setDateOfBirth(e.target.value)}
                required
                disabled={loading}
              />
            </div>
          </div>
          <div className={styles.modalFooter}>
            <button type="button" className={styles.secondary} onClick={onClose} disabled={loading}>
              Cancel
            </button>
            <button type="submit" className={styles.primary} disabled={loading}>
              {loading ? 'Creating...' : 'Create Person'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreatePersonModal; 