import React, { useState } from 'react';
import toast from 'react-hot-toast';
import styles from '../../../css/modal.module.css';

const CreateVehicleModal = ({ isOpen, onClose, onSubmit }) => {
  const [type, setType] = useState('CAR');
  const [make, setMake] = useState('');
  const [model, setModel] = useState('');
  const [color, setColor] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  if (!isOpen) return null;

  const handleInternalSubmit = (e) => {
    e.preventDefault();
    setErrorMessage('');
    if (!make.trim() || !model.trim() || !color.trim()) {
      setErrorMessage('Make, Model, and Color are required.');
      return;
    }
    onSubmit({ 
      type, 
      make, 
      model, 
      color
    });
    // Assuming onSubmit will trigger a mutation that has onCompleted/onError
    // If not, toast messages need to be handled where the mutation is defined (Vehicles.jsx)
  };
  
  const handleClose = () => {
    setType('CAR');
    setMake('');
    setModel('');
    setColor('');
    setErrorMessage('');
    onClose();
  };

  return (
    <div className={styles.modalBackdrop} onClick={handleClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={handleClose}>
          &times;
        </button>
        <div className={styles.modalHeader}>
          <h3>Create New Vehicle</h3>
        </div>
        <form onSubmit={handleInternalSubmit}>
          <div className={styles.modalBody}>
            {errorMessage && <p className={styles.errorMessage}>{errorMessage}</p>}
            
            <div className={styles.formGroup}>
              <label htmlFor="vehicleType">Type:</label>
              <select 
                id="vehicleType" 
                className={styles.darkInput}
                value={type} 
                onChange={(e) => setType(e.target.value)} 
                required
              >
                <option value="CAR">Car</option>
                <option value="BOAT">Boat</option>
                <option value="AIRCRAFT">Aircraft</option>
                <option value="BIKE">Bike</option>
              </select>
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="vehicleMake">Make:</label>
              <input
                type="text"
                id="vehicleMake"
                className={styles.darkInput}
                value={make}
                onChange={(e) => setMake(e.target.value)}
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="vehicleModel">Model:</label>
              <input
                type="text"
                id="vehicleModel"
                className={styles.darkInput}
                value={model}
                onChange={(e) => setModel(e.target.value)}
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="vehicleColor">Color:</label>
              <input
                type="text"
                id="vehicleColor"
                className={styles.darkInput}
                value={color}
                onChange={(e) => setColor(e.target.value)}
                required
              />
            </div>
            
          </div>
          <div className={styles.modalFooter}>
            <button type="button" className={styles.secondary} onClick={handleClose}>
              Cancel
            </button>
            <button type="submit" className={styles.primary}>
              Create Vehicle 
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateVehicleModal; 