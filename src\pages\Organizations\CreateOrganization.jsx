import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { CREATE_ORGANIZATION } from '../../../api/mutations';
import { GET_ORGANIZATIONS } from '../../../api/queries';
import styles from '../../../css/modal.module.css';

const CreateOrganizationModal = ({ isOpen, onClose }) => {
  const [name, setName] = useState('');
  const [foundingDate, setFoundingDate] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  const [createOrganization, { loading }] = useMutation(CREATE_ORGANIZATION, {
    refetchQueries: [{ query: GET_ORGANIZATIONS }],
    onError: (error) => {
      setErrorMessage(error.message || 'Could not create organization. Please try again.');
      toast.error(error.message || 'Could not create organization. Please try again.');
    },
    onCompleted: () => {
      onClose(); 
      setName('');
      setFoundingDate('');
      setErrorMessage('');
      toast.success('Organization created successfully!');
    },
  });

  if (!isOpen) return null;

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrorMessage('');
    if (!name.trim() || !foundingDate) {
      setErrorMessage('Name and Founding Date are required.');
      return;
    }
    try {
      await createOrganization({ variables: { name, foundingDate } });
    } catch (err) {
      
      console.error('Submission error:', err);
    }
  };

  return (
    <div className={styles.modalBackdrop} onClick={onClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={onClose}>
          &times;
        </button>
        <div className={styles.modalHeader}>
          <h2>Create New Organization</h2>
        </div>
        <form onSubmit={handleSubmit}>
          <div className={styles.modalBody}>
            {errorMessage && <p style={{ color: 'red' }}>{errorMessage}</p>}
            <div>
              <label htmlFor="orgName">Organization Name:</label>
              <input
                type="text"
                id="orgName"
                className={styles.darkInput}
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                disabled={loading}
              />
            </div>
            <div>
              <label htmlFor="orgFoundingDate">Founding Date:</label>
              <input
                type="date"
                id="orgFoundingDate"
                className={styles.darkInput}
                value={foundingDate}
                onChange={(e) => setFoundingDate(e.target.value)}
                required
                disabled={loading}
              />
            </div>
          </div>
          <div className={styles.modalFooter}>
            <button type="button" className={styles.secondary} onClick={onClose} disabled={loading}>
              Cancel
            </button>
            <button type="submit" className={styles.primary} disabled={loading}>
              {loading ? 'Creating...' : 'Create Organization'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateOrganizationModal; 