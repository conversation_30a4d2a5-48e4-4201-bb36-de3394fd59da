import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { SET_TASK_LEVEL } from '../../../api/mutations';
import styles from '../../../css/modal.module.css';

const SetTaskLevelModal = ({ isOpen, onClose, task, onRefetch }) => {
  const [level, setLevel] = useState('CL1');
  const [errorMessage, setErrorMessage] = useState('');

  const [setLevelMutation, { loading }] = useMutation(SET_TASK_LEVEL, {
    onCompleted: () => {
      onRefetch();
      onClose();
      setErrorMessage('');
      toast.success('Level updated successfully!');
    },
    onError: (error) => {
      setErrorMessage(error.message || 'Could not set level. Please try again.');
      toast.error(error.message || 'Could not set level. Please try again.');
    }
  });

  if (!isOpen || !task) return null;

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrorMessage('');
    setLevelMutation({ variables: { taskHiveId: task.hiveId, level } });
  };

  const handleClose = () => {
    setErrorMessage('');
    onClose();
  };

  return (
    <div className={styles.modalBackdrop} onClick={handleClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={handleClose}>&times;</button>
        <div className={styles.modalHeader}>
          <h3>Set Task Level</h3>
          <p style={{ fontSize: '0.9rem', color: '#ccc', margin: '0.5rem 0 0 0' }}>
            Task: {task.title}
          </p>
        </div>
        <form onSubmit={handleSubmit}>
          <div className={styles.modalBody}>
            {errorMessage && <p className={styles.errorMessage}>{errorMessage}</p>}
            <div className={styles.formGroup}>
              <label htmlFor="taskLevel">Level:</label>
              <select
                id="taskLevel"
                className={styles.darkInput}
                value={level}
                onChange={(e) => setLevel(e.target.value)}
                required
              >
                <option value="CL1">CL1</option>
                <option value="CL2">CL2</option>
                <option value="CL3">CL3</option>
                <option value="CL4">CL4</option>
                <option value="CL5">CL5</option>
                <option value="CL6">CL6</option>
                <option value="CL7">CL7</option>
                <option value="CL8">CL8</option>
                <option value="CL9">CL9</option>
                <option value="CLS">CLS</option>
                <option value="CLX">CLX</option>
              </select>
            </div>
          </div>
          <div className={styles.modalFooter}>
            <button type="button" className={styles.secondary} onClick={handleClose}>
              Cancel
            </button>
            <button type="submit" className={styles.primary} disabled={loading}>
              {loading ? 'Saving...' : 'Set Level'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SetTaskLevelModal;
