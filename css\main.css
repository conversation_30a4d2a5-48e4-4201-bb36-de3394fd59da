* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  margin:0px;
  height:100%;
  overflow: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}

body {
  background-color: #000;
  color: #fff;
  font-family: sans-serif;
}

.app-container {
  display: flex;
  min-height: 100vh;
  width: 100vw;
  position: relative;
}

.content {
  padding: 0px;
  flex: 1;
  background-color: #000;
  position: relative;
  overflow: auto;
  flex-direction: row;
  margin-top: 52px;
  margin-left: 269px;
  transition: all 0.2s ease;
  width: calc(100vw - 269px);
}

/* Adjust content when sidebar is collapsed */
.sidebar-is-collapsed ~ .content {
  margin-left: 65px;
  width: calc(100vw - 65px);
}

/* Adjust header when sidebar is collapsed */
.sidebar-is-collapsed ~ .content .header {
  left: 65px;

}

header,
section {
  padding: 8px;
}

img {
  max-width: 128px;
}

hr {
  border: 1px solid #faa006;
  margin-bottom: 10px;
  margin-top: 6px;
}