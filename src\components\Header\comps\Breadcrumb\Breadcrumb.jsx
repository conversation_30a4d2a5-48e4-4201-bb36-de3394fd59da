// Breadcrumb.jsx
import React from "react";
import { Link } from "react-router-dom";
import styles from '../../../../../css/breadcrumb.module.css';


const Breadcrumb = ({ pathSegments }) => {
  return (
    <nav className={styles.breadcrumb}>
      {pathSegments.map((segment, index) => {
        const isLast = index === pathSegments.length - 1;
        return (
          <span key={index} className={styles['breadcrumb-item']}>
            {isLast ? (
              <>
                {segment.icon}
                <span>{segment.label}</span>
              </>
            ) : (
              <Link to={segment.link}>
                       {segment.icon}
                       {segment.label}
              </Link>
            )}
            {!isLast && <span className={styles['breadcrumb-separator']}>{'>'}</span>}
          </span>
        );
      })}
    </nav>
  );
};

export default Breadcrumb;