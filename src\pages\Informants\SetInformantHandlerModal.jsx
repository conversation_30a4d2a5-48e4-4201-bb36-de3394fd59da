import React, { useState, useEffect } from 'react';
import styles from '../../../css/modal.module.css';

const SetInformantHandlerForm = ({ onSubmit, onCancel, currentHandlerHiveId, informantName, informantCodeName }) => {
  const [handlerHiveIdInput, setHandlerHiveIdInput] = useState(currentHandlerHiveId || '');
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    setHandlerHiveIdInput(currentHandlerHiveId || '');
  }, [currentHandlerHiveId]);

  const handleInternalSubmit = (e) => {
    e.preventDefault();
    setErrorMessage('');
    if (!handlerHiveIdInput.trim()) {
      setErrorMessage('Handler Hive ID is required.');
      return;
    }
    onSubmit({ handlerHiveId: handlerHiveIdInput.trim() });
  };

  return (
    <form onSubmit={handleInternalSubmit}>
        <div className={styles.modalHeader}>
            <h3>Assign Handler Agent</h3>
            {informantName && informantCodeName && (
                <p style={{fontSize: '0.6em',color: '#909090', marginTop: '4px', marginBottom: 0}}>
                    Informant: {informantName} ({informantCodeName})
                </p>
            )}
        </div>
        <div className={styles.modalBody}>
            {errorMessage && <p className={styles.errorMessage}>{errorMessage}</p>}
            <div className={styles.formGroup}>
                <label htmlFor="handlerHiveIdInput">Handler Hive ID:</label>
                <input 
                    type="text"
                    id="handlerHiveIdInput"
                    value={handlerHiveIdInput}
                    onChange={(e) => setHandlerHiveIdInput(e.target.value)}
                    className={styles.darkInput}
                    placeholder="Enter Agent Hive ID"
                    required 
                />
            </div>
        </div>
        <div className={styles.modalFooter}>
            <button type="button" className={styles.secondary} onClick={onCancel}>Cancel</button>
            <button type="submit" className={styles.primary}>Set Handler</button>
        </div>
    </form>
  );
};

const SetInformantHandlerModal = ({ isOpen, onClose, informant, onSubmitSetHandler }) => {
  if (!isOpen || !informant) return null;

  const currentHandlerHiveId = informant.handlerAgent && informant.handlerAgent.length > 0 ? informant.handlerAgent[0]?.hiveId : null;
  const informantNameDisplay = informant.backingPerson && informant.backingPerson.length > 0 
    ? `${informant.backingPerson[0].firstName} ${informant.backingPerson[0].lastName}` 
    : informant.codeName || 'Unknown Informant';
  const codeName = informant.codeName;

  const handleFormSubmitRelay = (formData) => {
    onSubmitSetHandler(formData);
  };

  return (
    <div className={styles.modalBackdrop} onClick={onClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={onClose}>&times;</button>
        
        <SetInformantHandlerForm
            onSubmit={handleFormSubmitRelay}
            onCancel={onClose}
            currentHandlerHiveId={currentHandlerHiveId}
            informantName={informantNameDisplay}
            informantCodeName={codeName}
        />
      </div>
    </div>
  );
};

export default SetInformantHandlerModal; 