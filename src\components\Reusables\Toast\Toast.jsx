import { Toaster } from "react-hot-toast";

export default function Toast() {
  return (
    <Toaster
      position="bottom-right"
      reverseOrder={false}
      gutter={8}
      containerStyle={{
        top: 10,
        left: 10,
        bottom: 20,
        right: 20,
      }}
      toastOptions={{
        style: {
          color: '#fff',
          border: '1px solid #333',
          borderRadius: '4px',
          padding: '7.6px',
          minWidth: '260px',
          minHeight: '46px',
          background: '#222',
          duration: 5000,
        },
        success: {
          duration: 5000,
          style: {},
          iconTheme: {
            primary: '#22c55e',
            secondary: '#222',
          },
        },
        error: {
          duration: 5000,
          style: {},
          iconTheme: {
            primary: '#ff4b4b',
            secondary: '#222',
          },
        },
        loading: {
          duration: 12000,
          style: {},
          iconTheme: {
            primary: '#eaa216',
            secondary: '#555',
          },
        },
      }}
    />
  );
}
