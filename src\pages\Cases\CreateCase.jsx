import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { CREATE_CASE } from '../../../api/mutations';
import { GET_CASES } from '../../../api/queries';
import styles from '../../../css/modal.module.css';

const CreateCaseModal = ({ isOpen, onClose }) => {
  const [title, setTitle] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  const [createCase, { loading }] = useMutation(CREATE_CASE, {
    refetchQueries: [{ query: GET_CASES }],
    onError: (error) => {
      setErrorMessage(error.message || 'Could not create case. Please try again.');
      toast.error(error.message || 'Could not create case. Please try again.');
    },
    onCompleted: () => {
      onClose(); 
      setTitle(''); 
      setErrorMessage('');
      toast.success('Case created successfully!');
    },
  });

  if (!isOpen) return null;

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrorMessage('');
    if (!title.trim()) {
      setErrorMessage('Title is required.');
      return;
    }
    try {
      await createCase({ variables: { title } });
    } catch (err) {
      // Error is handled by onError in useMutation
      console.error('Submission error:', err);
    }
  };

  return (
    <div className={styles.modalBackdrop} onClick={onClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={onClose}>
          &times;
        </button>
        <div className={styles.modalHeader}>
          <h3>Create New Case</h3>
        </div>
        <form onSubmit={handleSubmit}>
          <div className={styles.modalBody}>
            {errorMessage && <p style={{ color: 'red' }}>{errorMessage}</p>}
            <div>
              <label htmlFor="caseTitle">Case Title:</label>
              <input
                type="text"
                id="caseTitle"
                className={styles.darkInput}
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
                disabled={loading}
              />
            </div>
          </div>
          <div className={styles.modalFooter}>
            <button type="button" className={styles.secondary} onClick={onClose} disabled={loading}>
              Cancel
            </button>
            <button type="submit" className={styles.primary} disabled={loading}>
              {loading ? 'Creating...' : 'Create Case'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateCaseModal; 