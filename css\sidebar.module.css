.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  width: 269px;
  background-color: #222;
  transition: width 0.3s ease;
  border-right: 1px solid #333;
}

.sidebar.collapsed {
  width: 65px;
}

/* LOGO & NAME */
.logo-container {
  display: flex;
  align-items: center;
  padding: 12px 12px 12px 12px;
  margin-top: 8px;
  gap: 12px;
}  

.sidebar.collapsed .logo-container {
  margin-bottom: -8px;
}

.logo-img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.platform-name {
  font-size: 1.6rem;
  margin: 0px;
}

/* NAVIGATION AREA*/
.nav-items {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px 8px;
  padding-top: 12px;
}

/* NAV BUTTON */
.nav-button {
  display: flex;
  font-size: 14px;
  align-items: center;
  gap: 0.75rem;
  background: transparent;
  border: none;
  color: #ccc;
  padding: 0.75rem 1rem;
  margin: 0.25rem 0;
  text-align: left;
  cursor: pointer;
  border-radius: 6px;
  transition: background 0.15s, color 0.15s ease;
}

/* When the sidebar is collapsed, center the icon */
.sidebar.collapsed .nav-button { 
  justify-content: center;
}

/* Sidebar Title */
.sidebar-title {
  font-size: 1.6rem;
  color: #ccc;
  margin: 0px;
}

.nav-button:hover {
  background-color: #2e2e2e;
  color: #fff;
}

/* ACTIVE NAV BUTTON */
.nav-button.active {
  background-color: #3a3a3a;
  color: #eaa216;
}

/* ACTIVE+HOVER NAV BUTTON */
.nav-button.active:hover {
  background-color: #444;
  color: #f1c232;
}

/* COLLAPSE BUTTON */
.collapse-button {
  background-color: #222;
  border: none;
  color: #ccc;
  padding: 8px 9px 8px 7px;
  border-radius: 6px;
  cursor: pointer;
  height: 38px;
  width: 38px;
  transition: all 0.2s ease, color 0.2s ease;
}

.sidebar.collapsed .collapse-button {
  padding: 8px 7px 8px 9px;
}

.collapse-button:hover {
  background-color: #2e2e2e;
  color: #fff;
}

/* SEARCH BAR */
.search-bar {
  display: flex;
  align-items: center;
  padding: 8px;
  gap: 1.5px;
  position: relative;
}

/* Collapsed search bar */
.search-bar.collapsed {
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

/* BOTTOM PANEL */
.bottom-panel {
  background-color: #3a3a3a;
  display: flex;
  align-items: center;
  padding: 4px;
  margin: 8px;
  border-radius: 6px;
  gap: 0.2rem;
  position: relative;
}

/* Collapsed search bar */
.bottom-panel.collapsed {
  background-color: transparent;
  flex-direction: column-reverse;
  align-items: center;
  margin: 0px;
  gap: 4px;
  margin-bottom: 8px;
}

.search-icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Collapsed search: just the icon in the center */
.collapsed-search {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  padding-bottom: 20px;
}

/* Search input wrapper */
.search-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #3a3a3a;
  border-radius: 6px;
  padding: 8px;
  padding-right: 0px;
  height: 38px;
  border: 1.8px solid transparent; /* No border by default */
  transition: all 0.2s ease;
}

.search-input-wrapper:hover {
  border: 1.8px solid #4d4d4d;
}

/* Add border on input focus */
.search-input:focus + .search-input-wrapper,
.search-input-wrapper:focus-within {
  border-color: #835c0c;
  background-color: #333333;
}

/* The search input itself */
.search-input {
  border: none;
  background: transparent;
  color: #fff;
  padding: 8px;
  width: 100%;
}

.search-input:focus {
  outline: none;
}

.search-button {
  display: none;
  background-color: #222;
  border: none;
  color: #ccc;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  height: 38px;
  width: 38px;
  transition: background 0.2s ease, color 0.2s ease;
}

.search-button:hover {
  background-color: #2e2e2e;
  color: #eaa216;
}

.add-button {
  background-color: #3a3a3a;
  border: none;
  color: #ccc;
  padding: 6px;
  margin-left: 2.5px;
  cursor: pointer;
  border-radius: 6px;
  height: 38px;
  width: 38px;
  transition: background 0.2s ease, color 0.2s ease;
}

.add-button:hover {
  background-color: #444;
  color: #00862f;
}

/* Move add button under search button in collapsed mode */
.search-bar.collapsed .add-button {
  background-color: #222;
  margin-bottom: 8px;
  margin-left: 0px;
  width: 38px;
  height: 38px;
  text-align: center;
}

.search-bar.collapsed .add-button:hover {
  background-color: #2e2e2e;
}

/* Unhide when collapsed */
.search-bar.collapsed .search-button {
  display: unset;
  margin-top: 8px;
  width: 38px;
  height: 38px;
  text-align: center;
}

/* Profile Card */
.profile-card {
  display: flex;
  flex-direction: column;
  border-radius: 6px;
  margin: 8px;
  margin-bottom: 0px;
  padding: 4px;
  gap: 0px;
  overflow: hidden; /* Ensure content stays within bounds during transition */
  transition: all 0.12s ease;
  opacity: 1; /* Fully visible */
  transform: translateX(0); /* Neutral position */
}

.profile-card:hover {
  background-color: #2e2e2e;
}

.profile-card.collapsed {
  align-items: center; /* Center content in collapsed mode */
  gap: 0; /* Remove extra gap in collapsed mode */
  background-color: transparent;
  border-radius: 50%;
}

.sidebar.collapsed .profile-card.collapsed {
  height: 48px;
  width: 48px;
  margin-bottom: 4px;
}

.sidebar.collapsed .profile-card.collapsed:hover {
  background-color: #3a3a3a;
}

.sidebar.collapsed .profile-card.expanded {
  height: unset;
  width: unset;
}

.profile-card.expanded {
  background-color: #3a3a3a;
  border-radius: 6px;
  padding: 8px 4px 8px 4px;
  height: unset;
  width: unset;
}

/* Profile Header */
.profile-header {
  display: flex;
  align-items: center;
  cursor: pointer;
  gap: 8px;
  transition: all 0.1s ease;
}

.profile-header.collapsed {
  flex-direction: column; /* Stack elements vertically when collapsed */
}

.profile-picture {
  position: relative;
  width: 40px;
  height: 40px;
  transition: all 0.1s ease;
  margin-left: 1.6px;
}

.profile-card.expanded .profile-picture {
  position: relative;
  width: 40px;
  height: 40px;
}

.profile-picture img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

/* Status Indicator */
.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  border: 2px solid #222;
  border-radius: 50%;
}

.profile-card.expanded .status-indicator {
  border: 2px solid #444;
}

/* Profile Info */
.profile-info1 {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.profile-info2 {
  display: flex;
  flex: 1;
  flex-direction: row;
  gap: 2px;
}

.user-name {
  color: #fff;
  font-weight: bold;
  font-size: 14px;
}

.user-rank {
  color: #ccc;
  font-size: 12px;
  font-style: italic;
}

/* Toggle Button (Arrow) */
.toggle-button {
  background: none;
  border: none;
  color: #ccc;
  cursor: pointer;
  transition: all 0.5s ease; /* Smooth arrow flip */
  margin-left: auto;
  width: 20px;
  height: 20px;
}

/* Profile Buttons (Settings & Logout) */
.profile-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 8px;
  transition: all 0.3s ease; /* Smooth buttons slide in */
}

.logout-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #222;
  border: none;
  color: #ccc;
  padding: 8px 7px 8px 9px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s ease, color 0.2s ease;
}

.profile-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #222;
  border: none;
  color: #ccc;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s ease, color 0.2s ease;
}

.settings-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #2a2a2a;
  border: none;
  color: #ccc;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.2s ease, color 0.2s ease;
}

.profile-card.collapsed .settings-button {
  width: 36px;
}

.profile-button span,
.settings-button span,
.logout-button span {
  display: inline; /* Default: text visible */
}

.profile-card.collapsed .profile-button span,
.profile-card.collapsed .settings-button span,
.profile-card.collapsed .logout-button span {
  display: none; /* Hide text in collapsed mode */
}

.profile-button:hover {
  background-color: #2e2e2e;
  color: #fff;
}

.logout-button:hover {
  background-color: #5b1919;
  color: #fff;
}

.profile-card.expanded .logout-button {
  margin-top: -4px;
}

.sidebar.collapsed .logout-button {
  margin-top: 0px;
}

.sidebar.collapsed .profile-button {
  margin-top: 0px;
}

.sidebar.collapsed .profile-picture {
  margin-left: 0px;
  height: 40px;
  width: 40px;
}

.sidebar.collapsed .settings-button,
.sidebar.collapsed .profile-button {
  margin-top: 4px;
}

/* Bottom Panel Buttons */
.bottom-panel-button {
  background-color: #222;
  border: none;
  color: #ccc;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  height: 38px;
  width: 38px;
  transition: all 0.2s ease, color 0.2s ease;
}

.bottom-panel-button:hover {
  background-color: #2e2e2e;
  color: #fff;
}

.sidebar.collapsed .bottom-panel-button.bug {
  display: none;
}

.sidebar.collapsed .bottom-panel-button.theme {
  display: none;
}

/* Base danger button */
.modal-btn-danger,
.preview-actions button.danger,
button.danger {
  background-color: #3a1919;
  color: #fff;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Hover state for all danger buttons */
.modal-btn-danger:hover,
.preview-actions button.danger:hover,
button.danger:hover {
  background-color: #5b1919;
}

.search-result {
  margin: 4px 8px 0px 8px;
} 