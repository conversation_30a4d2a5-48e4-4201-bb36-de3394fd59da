import React, { useState } from 'react';
import { FiChevronDown, FiChevronUp, FiUser, FiLogOut } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../Auth/AuthContext';
import styles from '../../../../css/sidebar.module.css';

export default function ProfileCard({ isCollapsed, userName = 'John Doe', status = 'Online', rank = 'Agent' }) {
  const [isExpanded, setIsExpanded] = useState(false);
  const navigate = useNavigate();
  const { logout } = useAuth();

  const statusColors = {
    Online: 'green',
    Offline: 'red',
    Busy: 'orange',
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <div className={`${styles['profile-card']} ${isCollapsed ? styles.collapsed : ''} ${isExpanded ? styles.expanded : ''}`}>
      {isExpanded && (
        <div className={styles['profile-buttons']}>
          <button className={styles['logout-button']} onClick={handleLogout}>
            <FiLogOut size={20} />
            {!isCollapsed && <span>Logout</span>}
          </button>
          <button className={styles['profile-button']}>
            <FiUser size={20} />
            {!isCollapsed && <span>Profile</span>}
          </button>
        </div>
      )}
      <div className={styles['profile-header']} onClick={() => setIsExpanded(!isExpanded)}>
        <div className={styles['profile-picture']}>
          <img src="https://picsum.photos/44/44" alt="Profile" />
          <span className={styles['status-indicator']} style={{ backgroundColor: statusColors[status] || 'gray' }}></span>
        </div>
        {!isCollapsed && (
          <div className={styles['profile-info1']}>
            <span className={styles['user-name']}>{userName}</span>
            <span className={styles['user-rank']}>{rank}</span>
          </div>
        )}
        {!isCollapsed && (
          <div className={styles['profile-info2']}>
            <button className={styles['toggle-button']}>
              {isExpanded ? <FiChevronUp size={20} /> : <FiChevronDown size={20} />}
            </button>
          </div>
        )}
      </div>
    </div>
  );
}