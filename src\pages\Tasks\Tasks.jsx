import React, { useState } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { GET_TASKS } from '../../../api/queries';
import {
  DELETE_TASK,
  REMOVE_AGENT_FROM_TASK
} from '../../../api/mutations';
import CreateTaskModal from './CreateTaskModal';
import AssignAgentModal from './AssignAgentModal';
import SetTaskPriorityModal from './SetTaskPriorityModal';
import SetTaskLevelModal from './SetTaskLevelModal';
import SetTaskScopeModal from './SetTaskScopeModal';
import copyToClipboard from '../../utils/clipboard';
import '../../../css/shared.css';
import useModal from '../../hooks/useModal';

const Tasks = () => {
  const { loading, error, data, refetch } = useQuery(GET_TASKS);
  const [copiedId, setCopiedId] = useState(null);
  const [selectedTask, setSelectedTask] = useState(null);
  const { isOpen: isCreateModalOpen, open: openCreateModal, close: closeCreateModal } = useModal();
  const [isAssignAgentModalOpen, setIsAssignAgentModalOpen] = useState(false);
  const [isPriorityModalOpen, setIsPriorityModalOpen] = useState(false);
  const [isLevelModalOpen, setIsLevelModalOpen] = useState(false);
  const [isScopeModalOpen, setIsScopeModalOpen] = useState(false);

  const [deleteTaskMutation] = useMutation(DELETE_TASK, {
    onCompleted: () => {
      refetch();
      toast.success('Task deleted successfully!');
    },
    onError: (err) => {
      console.error('Error deleting task:', err);
      toast.error(`Error deleting task: ${err.message}`);
    }
  });

  const [removeAgentMutation] = useMutation(REMOVE_AGENT_FROM_TASK, {
    onCompleted: () => {
      refetch();
      toast.success('Agent removed from task.');
    },
    onError: (err) => {
      console.error('Error removing agent:', err);
      toast.error(`Error removing agent: ${err.message}`);
    }
  });

  const handleDeleteTask = (hiveId, title) => {
    if (window.confirm(`Are you sure you want to delete task "${title}" (ID: ${hiveId})?`)) {
      deleteTaskMutation({ variables: { hiveId } });
    }
  };

  const handleRemoveAgent = (task) => {
    if (window.confirm('Remove assigned agent from this task?')) {
      removeAgentMutation({ variables: { taskHiveId: task.hiveId } });
    }
  };


  if (loading) return <div style={{ padding: '1rem' }}>Loading tasks...</div>;
  if (error) return <div style={{ padding: '1rem' }}>Error: {error.message}</div>;


  const openAssignAgentModal = (task) => { setSelectedTask(task); setIsAssignAgentModalOpen(true); };
  const closeAssignAgentModal = () => { setIsAssignAgentModalOpen(false); setSelectedTask(null); };

  const openPriorityModal = (task) => { setSelectedTask(task); setIsPriorityModalOpen(true); };
  const closePriorityModal = () => { setIsPriorityModalOpen(false); setSelectedTask(null); };

  const openLevelModal = (task) => { setSelectedTask(task); setIsLevelModalOpen(true); };
  const closeLevelModal = () => { setIsLevelModalOpen(false); setSelectedTask(null); };

  const openScopeModal = (task) => { setSelectedTask(task); setIsScopeModalOpen(true); };
  const closeScopeModal = () => { setIsScopeModalOpen(false); setSelectedTask(null); };

  return (
    <div style={{ padding: '1rem' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h1>Tasks</h1>
        <button onClick={openCreateModal} className="button-primary">Create Task</button>
      </div>

      <CreateTaskModal isOpen={isCreateModalOpen} onClose={closeCreateModal} />

      {selectedTask && (
        <>
          <AssignAgentModal
            isOpen={isAssignAgentModalOpen}
            onClose={closeAssignAgentModal}
            task={selectedTask}
            onRefetch={refetch}
          />
          <SetTaskPriorityModal
            isOpen={isPriorityModalOpen}
            onClose={closePriorityModal}
            task={selectedTask}
            onRefetch={refetch}
          />
          <SetTaskLevelModal
            isOpen={isLevelModalOpen}
            onClose={closeLevelModal}
            task={selectedTask}
            onRefetch={refetch}
          />
          <SetTaskScopeModal
            isOpen={isScopeModalOpen}
            onClose={closeScopeModal}
            task={selectedTask}
            onRefetch={refetch}
          />
        </>
      )}

      <div style={{ marginTop: '2rem' }}>
        {data.tasks.map((task) => (
          <div
            key={task.hiveId}
            style={{
              padding: '1rem',
              marginBottom: '1rem',
              borderRadius: '4px',
              backgroundColor: '#222',
              position: 'relative'
            }}
          >
            <div style={{ position: 'absolute', top: '12px', right: '16px', display: 'flex', gap: '10px' }}>
              {task.assignedAgent && task.assignedAgent.length > 0 && (
                <button onClick={() => handleRemoveAgent(task)} className="button-delete" title="Remove Agent">Remove Agent</button>
              )}
              <button onClick={() => openAssignAgentModal(task)} className="button-utility" title="Assign Agent">Assign Agent</button>
              <button onClick={() => openPriorityModal(task)} className="button-utility" title="Set Priority">Set Prio</button>
              <button onClick={() => openLevelModal(task)} className="button-utility" title="Set Level">Set CL</button>
              <button onClick={() => openScopeModal(task)} className="button-utility" title="Set Scope">Set Scope</button>
              
              <button onClick={() => handleDeleteTask(task.hiveId, task.title)} className="button-delete" title="Delete Task">Delete</button>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginRight: '220px' }}>
              <h3>{task.title}</h3>
              <span
                style={{ fontSize: '0.8em', color: '#909090', cursor: 'pointer', userSelect: 'none' }}
                onClick={() => copyToClipboard(task.hiveId, setCopiedId)}
                title="Click to copy Hive ID"
              >
                {task.hiveId}
                {copiedId === task.hiveId && ' ✓'}
              </span>
            </div>
            <hr />
            <div style={{ display: 'flex', gap: '1rem' }}>
              <div style={{ flex: 1, padding: '1rem', backgroundColor: '#2a2a2a', borderRadius: '4px' }}>
                <p style={{ margin: '0.25rem 0', color: '#ccc' }}><strong>Description:</strong> {task.description || 'N/A'}</p>
                <p style={{ margin: '0.25rem 0', color: '#ccc' }}><strong>Priority:</strong> {task.priority}</p>
                <p style={{ margin: '0.25rem 0', color: '#ccc' }}><strong>Level:</strong> {task.level}</p>
                <p style={{ margin: '0.25rem 0', color: '#ccc' }}><strong>Created:</strong> {task.createdAt ? new Date(task.createdAt).toLocaleDateString() : 'Unknown'}</p>
              </div>
              <div style={{ flex: 1, padding: '1rem', backgroundColor: '#2a2a2a', borderRadius: '4px' }}>
                <div>
                  <strong>Assigned Agent:</strong>
                  {task.assignedAgent && task.assignedAgent.length > 0 ? (
                    <ul>
                      {task.assignedAgent.map(agent => (
                        <li style={{ marginLeft: '18px', color: '#ffda94' }} key={agent.hiveId}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <span>{agent.username}</span>
                            <span style={{ fontSize: '0.8em', color: '#909090', cursor: 'pointer', userSelect: 'none' }} onClick={() => copyToClipboard(agent.hiveId, setCopiedId)} title="Copy Agent ID">
                              {agent.hiveId}{copiedId === agent.hiveId && ' ✓'}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p style={{ color: '#909090' }}>None</p>
                  )}
                </div>
                <div style={{ marginTop: '0.5rem' }}>
                  <strong>Scope:</strong>
                  {task.scope && task.scope.length > 0 ? (
                    <ul>
                      {task.scope.map(s => {
                        const name = s.__typename === 'Case' ? s.title : s.title;
                        return (
                          <li style={{ marginLeft: '18px', color: '#ffda94' }} key={s.hiveId}>
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                              <span>{s.__typename}: {name}</span>
                              <span style={{ fontSize: '0.8em', color: '#909090', cursor: 'pointer', userSelect: 'none' }} onClick={() => copyToClipboard(s.hiveId, setCopiedId)} title="Copy ID">
                                {s.hiveId}{copiedId === s.hiveId && ' ✓'}
                              </span>
                            </div>
                          </li>
                        );
                      })}
                    </ul>
                  ) : (
                    <p style={{ color: '#909090' }}>None</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Tasks;
