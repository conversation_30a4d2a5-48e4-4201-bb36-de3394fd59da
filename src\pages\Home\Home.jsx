import React from 'react';
import { useQuery } from '@apollo/client';
import { GET_CASES, GET_PEOPLE, GET_AGENTS, GET_INFORMANTS } from '../../../api/queries';

export default function Home() {
  const { loading: casesLoading, error: casesError, data: casesData } = useQuery(GET_CASES);
  const { loading: peopleLoading, error: peopleError, data: peopleData } = useQuery(GET_PEOPLE);
  const { loading: agentsLoading, error: agentsError, data: agentsData } = useQuery(GET_AGENTS);
  const { loading: infLoading, error: infError, data: infData } = useQuery(GET_INFORMANTS);

  if (casesLoading || peopleLoading || agentsLoading || infLoading) {
    return <div style={{ padding: '1rem' }}>Loading dashboard...</div>;
  }

  if (casesError || peopleError || agentsError || infError) {
    return <div style={{ padding: '1rem' }}>Error loading metrics.</div>;
  }

  const cases = casesData?.cases || [];
  const totalCases = cases.length;
  const closedCases = cases.filter(c => c.status && c.status.toLowerCase() === 'closed').length;
  const closedPercent = totalCases > 0 ? Math.round((closedCases / totalCases) * 100) : 0;

  const totalPeople = peopleData?.people?.length || 0;
  const totalAgents = agentsData?.agents?.length || 0;
  const totalInformants = infData?.informants?.length || 0;

  return (
    <div style={{ padding: '1rem' }}>
      <h1>Home</h1>
      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '1rem', marginTop: '1rem' }}>
        <div style={{ backgroundColor: '#222', padding: '1rem', borderRadius: '4px', flex: '1 1 200px' }}>
          <h3>Cases</h3>
          <p>Total: {totalCases}</p>
          <p>Closed: {closedCases} ({closedPercent}%)</p>
        </div>
        <div style={{ backgroundColor: '#222', padding: '1rem', borderRadius: '4px', flex: '1 1 200px' }}>
          <h3>People</h3>
          <p>Total: {totalPeople}</p>
        </div>
        <div style={{ backgroundColor: '#222', padding: '1rem', borderRadius: '4px', flex: '1 1 200px' }}>
          <h3>Agents</h3>
          <p>Total: {totalAgents}</p>
        </div>
        <div style={{ backgroundColor: '#222', padding: '1rem', borderRadius: '4px', flex: '1 1 200px' }}>
          <h3>Informants</h3>
          <p>Total: {totalInformants}</p>
        </div>
      </div>
    </div>
  );
}
