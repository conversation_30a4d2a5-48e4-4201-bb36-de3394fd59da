/* Breadcrumb.css */
.breadcrumb {
  display: flex;
  align-items: center;
  flex: 1;
  padding: 4px;
  color: #666;
  background-color: #1a1a1a;
  border: 1px solid #333;
  border-radius: 8px;
  font-family: "Arial", sans-serif;
  font-size: 14px;
  font-weight: 500;
  gap: 4px;
  height: 32px;
  overflow-x: auto; /* Handle long breadcrumbs */
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

.breadcrumb::-webkit-scrollbar {
  visibility: hidden;
  height: 0px;
}

.breadcrumb::-webkit-scrollbar-thumb {
  visibility: hidden;
  background: #d6d6d6;
  border-radius: 4px;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: 0px;
}

.breadcrumb-item .svgx {
  margin-left: 0px;
}

.breadcrumb-item a {
  text-decoration: none;
  display: flex;
  align-items: center;
  color: #eaa216;
  padding: 3px 4px;
  border-radius: 4px;
  font-weight: bold;
  transition: all 0.2s;
  gap: 4px;
}

.breadcrumb-item a .svgx {
  margin-left: 0px;
}

.breadcrumb-item a:hover {
  background-color: #2e2e2e;
  color: #f1c232;
}

.breadcrumb-item a:focus {
  background-color: #3a3a3a;
}

.breadcrumb-item span {
  color: #666;
}

.breadcrumb-separator {
  font-weight: bold;
  color: #666;
  font-size: 16px;
}

.breadcrumb-item:nth-child(2) {
  font-weight: bold;
  color: #666;
  margin-left: 0px; 
}

.breadcrumb-item:last-child {
  margin-left: 4px; 
}

.breadcrumb-item:last-child span {
  font-weight: bold;
  margin-left: 0px; 
  color: #666;
}

/* Responsive Styling */
@media (max-width: 768px) {
  .breadcrumb {
    font-size: 10px;
  }

  .breadcrumb-item a {
    padding: 3px 4px;
  }

  .breadcrumb-separator {
    margin: 0 6px;
  }
}