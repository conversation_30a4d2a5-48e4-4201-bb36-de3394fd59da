import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { ADD_SCOPED_CASE_TO_OPERATION, REMOVE_SCOPED_CASE_FROM_OPERATION } from '../../../api/mutations';
import styles from '../../../css/modal.module.css';
import '../../../css/shared.css';

const ManageCaseScopeModal = ({ isOpen, onClose, operation, onRefetch }) => {
  const [caseHiveId, setCaseHiveId] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [confirmRemove, setConfirmRemove] = useState(null);

  const [addCaseScopeMutation, { loading: addLoading }] = useMutation(ADD_SCOPED_CASE_TO_OPERATION, {
    onCompleted: () => {
      onRefetch();
      setCaseHiveId('');
      setErrorMessage('');
      toast.success('Case added to operation scope successfully!');
    },
    onError: (error) => {
      setErrorMessage(error.message || 'Could not add case to operation scope. Please try again.');
      toast.error(error.message || 'Could not add case to operation scope. Please try again.');
    }
  });

  const [removeCaseScopeMutation, { loading: removeLoading }] = useMutation(REMOVE_SCOPED_CASE_FROM_OPERATION, {
    onCompleted: () => {
      onRefetch();
      setConfirmRemove(null);
      toast.success('Case removed from operation scope successfully!');
    },
    onError: (error) => {
      console.error('Error removing case scope:', error);
      toast.error(`Error removing case scope: ${error.message}`);
      setConfirmRemove(null);
    }
  });

  if (!isOpen || !operation) return null;

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrorMessage('');
    
    if (!caseHiveId.trim()) {
      setErrorMessage('Case Hive ID is required.');
      return;
    }

    // Check if case is already in the operation scope
    const isAlreadyInScope = operation.scopedToCase?.some(caseItem => caseItem.hiveId === caseHiveId.trim());
    if (isAlreadyInScope) {
      setErrorMessage('This case is already in the operation scope.');
      return;
    }

    addCaseScopeMutation({
      variables: {
        operationHiveId: operation.hiveId,
        caseHiveId: caseHiveId.trim()
      }
    });
  };

  const handleRemove = (caseItem) => {
    setConfirmRemove(caseItem);
  };

  const confirmRemoveAction = () => {
    if (confirmRemove) {
      removeCaseScopeMutation({
        variables: {
          caseHiveId: confirmRemove.hiveId,
          operationHiveId: operation.hiveId
        }
      });
    }
  };

  const cancelRemove = () => {
    setConfirmRemove(null);
  };

  const handleClose = () => {
    setCaseHiveId('');
    setErrorMessage('');
    setConfirmRemove(null);
    onClose();
  };

  return (
    <div className={styles.modalBackdrop} onClick={handleClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={handleClose}>
          &times;
        </button>
        <div className={styles.modalHeader}>
          <h3>Manage Case Scope for Operation</h3>
          <p style={{ fontSize: '0.9rem', color: '#ccc', margin: '0.5rem 0 0 0' }}>
            Operation: {operation.title}
          </p>
        </div>
        <div className={styles.modalBody}>
          {errorMessage && <p className={styles.errorMessage}>{errorMessage}</p>}

          {/* Current Cases in Scope Section */}
          <div style={{ marginBottom: '1.5rem' }}>
            <label style={{ color: '#ccc', fontSize: '0.9rem', fontWeight: 'bold' }}>Current Cases in Scope:</label>
            {operation.scopedToCase && operation.scopedToCase.length > 0 ? (
              <div style={{
                margin: '0.5rem 0',
                padding: '0.5rem',
                backgroundColor: '#2a2a2a',
                borderRadius: '4px',
                border: '1px solid #444'
              }}>
                {operation.scopedToCase.map((caseItem) => (
                  <div key={caseItem.hiveId} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '0.5rem 0',
                    borderBottom: '1px solid #333'
                  }}>
                    <span style={{ color: '#e0e0e0', fontSize: '0.9rem' }}>
                      {caseItem.title} (Status: {caseItem.status}, ID: {caseItem.hiveId})
                    </span>
                    <button
                      type="button"
                      onClick={() => handleRemove(caseItem)}
                      className="button-remove-small"
                      title="Remove case from scope"
                    >
                      -
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <p style={{ color: '#888', fontSize: '0.9rem', margin: '0.5rem 0', fontStyle: 'italic' }}>
                No cases in scope for this operation.
              </p>
            )}
          </div>

          {/* Add New Case Section */}
          <form onSubmit={handleSubmit}>
            <div style={{ borderTop: '1px solid #444', paddingTop: '1rem' }}>
              <label style={{ color: '#ccc', fontSize: '0.9rem', fontWeight: 'bold' }}>Add New Case to Scope:</label>
              <div className={styles.formGroup} style={{ marginTop: '0.5rem' }}>
                <label htmlFor="caseHiveId">Case Hive ID:</label>
                <input
                  type="text"
                  id="caseHiveId"
                  className={styles.darkInput}
                  value={caseHiveId}
                  onChange={(e) => setCaseHiveId(e.target.value)}
                  placeholder="Enter Case Hive ID"
                  required
                />
              </div>
            </div>
            <div className={styles.modalFooter}>
              <button type="button" className={styles.secondary} onClick={handleClose}>
                Close
              </button>
              <button type="submit" className={styles.primary} disabled={addLoading}>
                {addLoading ? 'Adding...' : 'Add Case'}
              </button>
            </div>
          </form>
        </div>

        {/* Confirmation Dialog */}
        {confirmRemove && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1001
          }}>
            <div style={{
              backgroundColor: '#222',
              padding: '1.5rem',
              borderRadius: '8px',
              maxWidth: '400px',
              width: '90%'
            }}>
              <h4 style={{ color: '#fff', marginBottom: '1rem' }}>Confirm Removal</h4>
              <p style={{ color: '#ccc', marginBottom: '1.5rem' }}>
                Are you sure you want to remove case <strong>{confirmRemove.title}</strong> from this operation's scope?
              </p>
              <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
                <button
                  onClick={cancelRemove}
                  className={styles.primary}
                  style={{ backgroundColor: '#3a3a3a' }}
                >
                  Cancel
                </button>
                <button
                  onClick={confirmRemoveAction}
                  className={styles.secondary}
                  disabled={removeLoading}
                >
                  {removeLoading ? 'Removing...' : 'Remove'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ManageCaseScopeModal;
