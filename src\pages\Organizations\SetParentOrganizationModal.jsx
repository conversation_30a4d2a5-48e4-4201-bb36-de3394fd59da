import React, { useState, useEffect } from 'react';
import styles from '../../../css/modal.module.css';

const SetParentOrganizationForm = ({ onSubmit, onCancel, currentParentHiveId, organizationName }) => {
  const [parentHiveIdInput, setParentHiveIdInput] = useState(currentParentHiveId || '');
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    setParentHiveIdInput(currentParentHiveId || '');
  }, [currentParentHiveId]);

  const handleInternalSubmit = (e) => {
    e.preventDefault();
    setErrorMessage('');
    if (!parentHiveIdInput.trim()) {
      setErrorMessage('Parent Organization Hive ID is required.');
      return;
    }
    onSubmit({ parentOrganizationHiveId: parentHiveIdInput.trim() });
  };

  return (
    <form onSubmit={handleInternalSubmit}>
        <div className={styles.modalHeader}>
            <h4>Set Parent for {organizationName}</h4>
        </div>
        <div className={styles.modalBody}>
            {errorMessage && <p className={styles.errorMessage}>{errorMessage}</p>}
            <div className={styles.formGroup}>
                <label htmlFor="parentHiveIdInput">Parent Organization Hive ID:</label>
                <input 
                    type="text"
                    id="parentHiveIdInput"
                    value={parentHiveIdInput}
                    onChange={(e) => setParentHiveIdInput(e.target.value)}
                    className={styles.darkInput}
                    placeholder="Enter Parent Hive ID"
                    required 
                />
            </div>
        </div>
        <div className={styles.modalFooter}>
            <button type="button" className={styles.secondary} onClick={onCancel}>Cancel</button>
            <button type="submit" className={styles.primary}>Set Parent</button>
        </div>
    </form>
  );
};

const SetParentOrganizationModal = ({ isOpen, onClose, organization, onSubmitSetParent }) => {
  if (!isOpen || !organization) return null;

  // Assuming parentOrganization is an array and we take the first one, or null if not present
  const currentParentHiveId = organization.parentOrganization && organization.parentOrganization.length > 0 
                            ? organization.parentOrganization[0]?.hiveId 
                            : '';
                            
  const organizationName = organization.name;

  const handleFormSubmitRelay = (formData) => {
    onSubmitSetParent(formData);
  };

  return (
    <div className={styles.modalBackdrop} onClick={onClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={onClose}>&times;</button>
        
        <SetParentOrganizationForm
            onSubmit={handleFormSubmitRelay}
            onCancel={onClose}
            currentParentHiveId={currentParentHiveId}
            organizationName={organizationName}
        />
      </div>
    </div>
  );
};

export default SetParentOrganizationModal; 