import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { ASSIGN_AGENT_TO_TASK } from '../../../api/mutations';
import styles from '../../../css/modal.module.css';

const AssignAgentModal = ({ isOpen, onClose, task, onRefetch }) => {
  const [agentHiveId, setAgentHiveId] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  const [assignAgent, { loading }] = useMutation(ASSIGN_AGENT_TO_TASK, {
    onCompleted: () => {
      onRefetch();
      onClose();
      setAgentHiveId('');
      setErrorMessage('');
      toast.success('Agent assigned successfully!');
    },
    onError: (error) => {
      setErrorMessage(error.message || 'Could not assign agent. Please try again.');
      toast.error(error.message || 'Could not assign agent. Please try again.');
    }
  });

  if (!isOpen || !task) return null;

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrorMessage('');
    if (!agentHiveId.trim()) {
      setErrorMessage('Agent Hive ID is required.');
      return;
    }
    assignAgent({ variables: { taskHiveId: task.hiveId, agentHiveId: agentHiveId.trim() } });
  };

  const handleClose = () => {
    setAgentHiveId('');
    setErrorMessage('');
    onClose();
  };

  const currentAgent = task.assignedAgent && task.assignedAgent.length > 0 ? task.assignedAgent[0] : null;

  return (
    <div className={styles.modalBackdrop} onClick={handleClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={handleClose}>&times;</button>
        <div className={styles.modalHeader}>
          <h3>Assign Agent</h3>
          <p style={{ fontSize: '0.9rem', color: '#ccc', margin: '0.5rem 0 0 0' }}>
            Task: {task.title}
          </p>
        </div>
        <form onSubmit={handleSubmit}>
          <div className={styles.modalBody}>
            {errorMessage && <p className={styles.errorMessage}>{errorMessage}</p>}
            <div className={styles.formGroup}>
              <label htmlFor="agentHiveId">Agent Hive ID:</label>
              <input
                type="text"
                id="agentHiveId"
                className={styles.darkInput}
                value={agentHiveId}
                onChange={(e) => setAgentHiveId(e.target.value)}
                placeholder="Enter Agent Hive ID"
                required
              />
            </div>
            {currentAgent && (
              <div style={{ marginTop: '1rem' }}>
                <label style={{ color: '#ccc', fontSize: '0.9rem' }}>Current Agent:</label>
                <div style={{ margin: '0.5rem 0', padding: '0.5rem', backgroundColor: '#333', borderRadius: '4px' }}>
                  <p style={{ margin: 0, fontSize: '0.8rem', color: '#ccc' }}>
                    {currentAgent.username}
                    <br />
                    <span style={{ color: '#888' }}>ID: {currentAgent.hiveId}</span>
                  </p>
                </div>
                <small style={{ color: '#888', fontSize: '0.8rem' }}>
                  Assigning a new agent will replace the current one.
                </small>
              </div>
            )}
          </div>
          <div className={styles.modalFooter}>
            <button type="button" className={styles.secondary} onClick={handleClose}>
              Cancel
            </button>
            <button type="submit" className={styles.primary} disabled={loading}>
              {loading ? 'Assigning...' : 'Assign Agent'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AssignAgentModal;
