import React, { useState } from 'react';
import { useMutation } from '@apollo/client';
import toast from 'react-hot-toast';
import { CREATE_OPERATION } from '../../../api/mutations';
import { GET_OPERATIONS } from '../../../api/queries';
import styles from '../../../css/modal.module.css';

const CreateOperationModal = ({ isOpen, onClose }) => {
  const [title, setTitle] = useState('');
  const [type, setType] = useState('SURVEILLANCE');
  const [errorMessage, setErrorMessage] = useState('');

  const [createOperation, { loading }] = useMutation(CREATE_OPERATION, {
    refetchQueries: [{ query: GET_OPERATIONS }],
    onError: (error) => {
      setErrorMessage(error.message || 'Could not create operation. Please try again.');
      toast.error(error.message || 'Could not create operation. Please try again.');
    },
    onCompleted: () => {
      onClose(); 
      setTitle('');
      setType('SURVEILLANCE');
      setErrorMessage('');
      toast.success('Operation created successfully!');
    },
  });

  if (!isOpen) return null;

  const handleSubmit = (e) => {
    e.preventDefault();
    setErrorMessage('');
    
    if (!title.trim()) {
      setErrorMessage('Title is required.');
      return;
    }

    createOperation({
      variables: {
        title: title.trim(),
        type
      }
    });
  };

  const handleClose = () => {
    setTitle('');
    setType('SURVEILLANCE');
    setErrorMessage('');
    onClose();
  };

  return (
    <div className={styles.modalBackdrop} onClick={handleClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <button className={styles.modalCloseButton} onClick={handleClose}>
          &times;
        </button>
        <div className={styles.modalHeader}>
          <h3>Create New Operation</h3>
        </div>
        <form onSubmit={handleSubmit}>
          <div className={styles.modalBody}>
            {errorMessage && <p className={styles.errorMessage}>{errorMessage}</p>}
            
            <div className={styles.formGroup}>
              <label htmlFor="operationTitle">Title:</label>
              <input
                type="text"
                id="operationTitle"
                className={styles.darkInput}
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Enter operation title"
                required
              />
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="operationType">Type:</label>
              <select
                id="operationType"
                className={styles.darkInput}
                value={type}
                onChange={(e) => setType(e.target.value)}
                required
              >
                <option value="SURVEILLANCE">Surveillance</option>
                <option value="APPREHENSION">Apprehension</option>
                <option value="UNDERCOVER">Undercover</option>
                <option value="SPECIAL">Special</option>
              </select>
            </div>
          </div>
          <div className={styles.modalFooter}>
            <button type="button" className={styles.secondary} onClick={handleClose}>
              Cancel
            </button>
            <button type="submit" className={styles.primary} disabled={loading}>
              {loading ? 'Creating...' : 'Create Operation'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateOperationModal;
