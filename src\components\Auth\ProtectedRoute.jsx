import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from './AuthContext';

export default function ProtectedRoute({ children, requireAdmin = false, requireDeveloper = false }) {
  const { isAuthenticated, isAdmin, isDeveloper } = useAuth();

  if (!isAuthenticated()) {
    return <Navigate to="/login" replace />;
  }

  if (requireAdmin && !isAdmin()) {
    return <Navigate to="/" replace />;
  }

  if (requireDeveloper && !isDeveloper()) {
    return <Navigate to="/" replace />;
  }

  return children;
} 